"""
Agent Orchestrator for Metamorphic Reactor
Manages multi-agent conversations and consensus building
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, AsyncGenerator
from datetime import datetime, timedelta
import uuid

from .agent_types import (
    AgentRole, TaskStatus, AgentStatus, ConsensusLevel,
    AgentConfig, AgentResponse, ConversationRound, TaskContext
)
from .agent_factory import agent_factory
from .base_agent import BaseAgent
from ..utils.conversation_manager import conversation_manager
from ..services import rate_limiter, safety_guardian, LimitScope

logger = logging.getLogger(__name__)

class AgentOrchestrator:
    """
    Orchestrates multi-agent conversations for autonomous consensus building
    
    Manages the "Plan → Act → Critique → Revise" loop until agents reach
    unanimous consensus or escalate to Meta-Block for human intervention.
    """
    
    def __init__(self):
        self.active_tasks: Dict[str, TaskContext] = {}
        self.task_agents: Dict[str, Dict[str, BaseAgent]] = {}
        self.conversation_streams: Dict[str, asyncio.Queue] = {}
        self.shutdown_flag = False
        
        # Configuration
        self.default_max_rounds = 10
        self.default_timeout_minutes = 15
        self.consensus_threshold = 1.0  # Require unanimous agreement
        self.meta_block_threshold = 3   # Trigger meta-block after 3 failed rounds
    
    async def initialize(self):
        """Initialize the orchestrator"""
        logger.info("Initializing Agent Orchestrator")
        
        # Initialize conversation manager
        await conversation_manager.initialize()
        
        # Initialize services
        await rate_limiter.initialize()
        await safety_guardian.initialize()
        
        # Perform any initialization tasks
        await self._setup_monitoring()
        
        logger.info("Agent Orchestrator initialized successfully")
    
    async def create_task(
        self,
        task_id: str,
        description: str,
        context_files: List[str] = None,
        max_rounds: int = None,
        timeout_minutes: int = None,
        require_consensus: bool = True,
        agent_config: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Create a new multi-agent task
        
        Args:
            task_id: Unique task identifier
            description: Task description
            context_files: List of context file paths
            max_rounds: Maximum conversation rounds
            timeout_minutes: Task timeout in minutes
            require_consensus: Whether to require unanimous consensus
            agent_config: Additional agent configuration
            
        Returns:
            Dict containing task creation result
        """
        
        if task_id in self.active_tasks:
            raise ValueError(f"Task {task_id} already exists")
        
        # Create task context
        task_context = TaskContext(
            task_id=task_id,
            description=description,
            max_rounds=max_rounds or self.default_max_rounds,
            timeout_minutes=timeout_minutes or self.default_timeout_minutes,
            require_consensus=require_consensus,
            context_files=context_files or [],
            status=TaskStatus.PENDING
        )
        
        # Determine team size based on configuration
        team_size = agent_config.get("team_size", 3) if agent_config else 3
        
        # Create agent team
        try:
            agents = agent_factory.create_agent_team(
                task_description=description,
                team_size=team_size,
                task_id=task_id,
                context_files=context_files or [],
                **(agent_config or {})
            )
            
            # Store task and agents
            self.active_tasks[task_id] = task_context
            self.task_agents[task_id] = agents
            self.conversation_streams[task_id] = asyncio.Queue()
            
            # Update task context with agent info
            task_context.participating_agents = list(agents.keys())
            for role_or_id, agent in agents.items():
                task_context.agent_configs[agent.agent_id] = agent.config
            
            logger.info(f"Created task {task_id} with {len(agents)} agents")
            
            return {
                "task_id": task_id,
                "status": task_context.status.value,
                "agents": [
                    {
                        "agent_id": agent.agent_id,
                        "role": agent.role.value,
                        "session_id": agent.session_id
                    }
                    for agent in agents.values()
                ],
                "team_size": len(agents),
                "created_at": task_context.created_at
            }
            
        except Exception as e:
            logger.error(f"Failed to create task {task_id}: {e}")
            raise
    
    async def execute_task(self, task_id: str) -> Dict[str, Any]:
        """
        Execute multi-agent conversation for a task
        
        Args:
            task_id: Task identifier
            
        Returns:
            Dict containing execution results
        """
        
        if task_id not in self.active_tasks:
            raise ValueError(f"Task {task_id} not found")
        
        task_context = self.active_tasks[task_id]
        agents = self.task_agents[task_id]
        
        try:
            # Check rate limits before starting task
            rate_check = await rate_limiter.check_rate_limit(
                scope=LimitScope.TASK,
                scope_id=task_id,
                request_tokens=100,  # Estimated initial tokens
                context={"task_description": task_context.description}
            )
            
            if not rate_check.allowed:
                raise Exception(f"Rate limit exceeded: {rate_check.limit_hit.limit_type.value}")
            
            # Safety check on task description
            safety_check = await safety_guardian.check_content_safety(
                content=task_context.description,
                task_id=task_id,
                context={"operation": "task_execution"}
            )
            
            if not safety_check.allowed:
                task_context.status = TaskStatus.FAILED
                raise Exception(f"Task blocked by safety guardian: {safety_check.overall_level.value}")
            
            task_context.status = TaskStatus.RUNNING
            task_context.started_at = datetime.utcnow()
            
            logger.info(f"Starting task execution: {task_id}")
            
            # Execute conversation rounds
            result = await self._execute_conversation_rounds(task_context, agents)
            
            # Update final status
            if result["consensus_reached"]:
                task_context.status = TaskStatus.COMPLETED
                task_context.final_consensus = result["final_response"]
                task_context.consensus_score = result["consensus_score"]
            elif result["meta_block_triggered"]:
                task_context.status = TaskStatus.META_BLOCK
            else:
                task_context.status = TaskStatus.FAILED
            
            task_context.completed_at = datetime.utcnow()
            
            # Save final task state to conversation history
            await conversation_manager.save_task(task_context)
            
            logger.info(f"Task {task_id} completed with status: {task_context.status.value}")
            
            return result
            
        except Exception as e:
            task_context.status = TaskStatus.FAILED
            task_context.completed_at = datetime.utcnow()
            logger.error(f"Task execution failed for {task_id}: {e}")
            raise
    
    async def _execute_conversation_rounds(
        self, 
        task_context: TaskContext, 
        agents: Dict[str, BaseAgent]
    ) -> Dict[str, Any]:
        """Execute the main conversation loop"""
        
        conversation_history = []
        consensus_reached = False
        meta_block_triggered = False
        final_response = None
        final_consensus_score = 0.0
        
        # Get ordered agents (planner, critic, additional)
        agent_order = self._get_agent_execution_order(agents)
        
        for round_num in range(1, task_context.max_rounds + 1):
            logger.info(f"Starting round {round_num} for task {task_context.task_id}")
            
            task_context.current_round = round_num
            
            # Create new conversation round
            round_context = ConversationRound(
                round_number=round_num,
                task_id=task_context.task_id
            )
            
            # Execute agent responses in order
            current_proposals = []
            
            for agent in agent_order:
                try:
                    # Generate agent response
                    response = await agent.process_task(
                        task_context, conversation_history, current_proposals
                    )
                    
                    current_proposals.append(response)
                    round_context.responses.append(response)
                    
                    # Stream response to subscribers
                    await self._stream_response(task_context.task_id, response)
                    
                    logger.debug(f"Agent {agent.agent_id} completed response in round {round_num}")
                    
                except Exception as e:
                    logger.error(f"Agent {agent.agent_id} failed in round {round_num}: {e}")
                    # Continue with other agents
                    continue
            
            # Analyze round results
            consensus_analysis = self._analyze_consensus(current_proposals)
            round_context.consensus_level = consensus_analysis["level"]
            round_context.consensus_score = consensus_analysis["score"]
            round_context.completed_at = datetime.utcnow()
            round_context.is_complete = True
            
            # Check for consensus
            if consensus_analysis["unanimous"]:
                consensus_reached = True
                final_response = self._synthesize_final_response(current_proposals)
                final_consensus_score = consensus_analysis["score"]
                round_context.requires_meta_block = False
                break
            
            # Check for meta-block conditions
            if self._should_trigger_meta_block(task_context, round_context, round_num):
                meta_block_triggered = True
                round_context.requires_meta_block = True
                round_context.meta_block_reason = self._get_meta_block_reason(
                    task_context, round_context, round_num
                )
                break
            
            # Add round to task context
            task_context.conversation_rounds.append(round_context)
            
            # Save round to conversation history
            await conversation_manager.save_conversation_round(task_context.task_id, round_context)
            
            # Update conversation history
            conversation_history.extend([
                {
                    "round": round_num,
                    "agent_id": response.agent_id,
                    "role": response.role.value,
                    "content": response.content,
                    "consensus_score": response.consensus_score,
                    "timestamp": response.timestamp.isoformat()
                }
                for response in current_proposals
            ])
            
            # Brief pause between rounds
            await asyncio.sleep(0.5)
        
        # Final round processing
        if round_context:
            task_context.conversation_rounds.append(round_context)
            # Save final round if it wasn't already saved
            if round_context not in [r for r in task_context.conversation_rounds[:-1]]:
                await conversation_manager.save_conversation_round(task_context.task_id, round_context)
        
        return {
            "consensus_reached": consensus_reached,
            "meta_block_triggered": meta_block_triggered,
            "final_response": final_response,
            "consensus_score": final_consensus_score,
            "rounds_completed": round_num,
            "conversation_history": conversation_history
        }
    
    def _get_agent_execution_order(self, agents: Dict[str, BaseAgent]) -> List[BaseAgent]:
        """Get ordered list of agents for execution"""
        
        ordered_agents = []
        
        # Always start with planner
        for agent in agents.values():
            if agent.role == AgentRole.PLANNER:
                ordered_agents.append(agent)
                break
        
        # Then critic
        for agent in agents.values():
            if agent.role == AgentRole.CRITIC:
                ordered_agents.append(agent)
                break
        
        # Then additional agents
        for agent in agents.values():
            if agent.role == AgentRole.ADDITIONAL:
                ordered_agents.append(agent)
        
        return ordered_agents
    
    def _analyze_consensus(self, responses: List[AgentResponse]) -> Dict[str, Any]:
        """Analyze consensus level among agent responses"""
        
        if not responses:
            return {
                "level": ConsensusLevel.NONE,
                "score": 0.0,
                "unanimous": False,
                "details": "No responses to analyze"
            }
        
        # Calculate consensus metrics
        agreement_count = sum(1 for r in responses if r.agrees_with_proposal)
        total_responses = len(responses)
        consensus_scores = [r.consensus_score for r in responses]
        average_consensus = sum(consensus_scores) / len(consensus_scores)
        
        # Determine consensus level
        agreement_ratio = agreement_count / total_responses
        
        if agreement_ratio == 1.0 and average_consensus >= 0.8:
            level = ConsensusLevel.UNANIMOUS
            unanimous = True
        elif agreement_ratio >= 0.6:
            level = ConsensusLevel.MAJORITY
            unanimous = False
        elif agreement_ratio > 0.0:
            level = ConsensusLevel.PARTIAL
            unanimous = False
        else:
            level = ConsensusLevel.NONE
            unanimous = False
        
        return {
            "level": level,
            "score": average_consensus,
            "unanimous": unanimous,
            "agreement_ratio": agreement_ratio,
            "details": f"{agreement_count}/{total_responses} agents agree"
        }
    
    def _should_trigger_meta_block(
        self, 
        task_context: TaskContext, 
        round_context: ConversationRound,
        round_num: int
    ) -> bool:
        """Determine if Meta-Block should be triggered"""
        
        # Check maximum rounds
        if round_num >= task_context.max_rounds:
            return True
        
        # Check for persistent low consensus
        if len(task_context.conversation_rounds) >= self.meta_block_threshold:
            recent_rounds = task_context.conversation_rounds[-self.meta_block_threshold:]
            recent_scores = [r.consensus_score for r in recent_rounds]
            
            if all(score < 0.5 for score in recent_scores):
                return True
        
        # Check for timeout
        if task_context.started_at:
            elapsed = datetime.utcnow() - task_context.started_at
            timeout = timedelta(minutes=task_context.timeout_minutes)
            
            if elapsed > timeout:
                return True
        
        return False
    
    def _get_meta_block_reason(
        self, 
        task_context: TaskContext, 
        round_context: ConversationRound,
        round_num: int
    ) -> str:
        """Generate Meta-Block reason description"""
        
        reasons = []
        
        if round_num >= task_context.max_rounds:
            reasons.append(f"Maximum rounds ({task_context.max_rounds}) reached")
        
        if len(task_context.conversation_rounds) >= self.meta_block_threshold:
            recent_rounds = task_context.conversation_rounds[-self.meta_block_threshold:]
            recent_scores = [r.consensus_score for r in recent_rounds]
            
            if all(score < 0.5 for score in recent_scores):
                avg_score = sum(recent_scores) / len(recent_scores)
                reasons.append(f"Persistent low consensus (avg {avg_score:.2f}) over {len(recent_scores)} rounds")
        
        if task_context.started_at:
            elapsed = datetime.utcnow() - task_context.started_at
            timeout = timedelta(minutes=task_context.timeout_minutes)
            
            if elapsed > timeout:
                reasons.append(f"Task timeout ({task_context.timeout_minutes} minutes)")
        
        return "; ".join(reasons) if reasons else "Meta-Block triggered"
    
    def _synthesize_final_response(self, responses: List[AgentResponse]) -> str:
        """Synthesize final consensus response from agent responses"""
        
        if not responses:
            return "No consensus achieved - no responses available"
        
        # Find the highest confidence response or latest planner response
        planner_responses = [r for r in responses if r.role == AgentRole.PLANNER]
        
        if planner_responses:
            return planner_responses[-1].content
        else:
            # Fall back to highest confidence response
            best_response = max(responses, key=lambda r: r.confidence)
            return best_response.content
    
    async def _stream_response(self, task_id: str, response: AgentResponse):
        """Stream response to task subscribers"""
        
        if task_id in self.conversation_streams:
            try:
                await self.conversation_streams[task_id].put({
                    "type": "agent_response",
                    "task_id": task_id,
                    "agent_id": response.agent_id,
                    "role": response.role.value,
                    "content": response.content,
                    "consensus_score": response.consensus_score,
                    "timestamp": response.timestamp.isoformat()
                })
            except Exception as e:
                logger.error(f"Error streaming response for task {task_id}: {e}")
    
    async def stream_task_tokens(self, task_id: str) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream real-time tokens from task execution"""
        
        if task_id not in self.conversation_streams:
            raise ValueError(f"Task {task_id} not found or not active")
        
        stream = self.conversation_streams[task_id]
        
        try:
            while True:
                try:
                    # Wait for next token with timeout
                    token_data = await asyncio.wait_for(stream.get(), timeout=1.0)
                    yield token_data
                    
                except asyncio.TimeoutError:
                    # Check if task is still active
                    if task_id not in self.active_tasks:
                        break
                    
                    task_context = self.active_tasks[task_id]
                    if task_context.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.META_BLOCK]:
                        break
                    
                    # Send keepalive
                    yield {"type": "keepalive", "timestamp": datetime.utcnow().isoformat()}
                    
        except Exception as e:
            logger.error(f"Error streaming tokens for task {task_id}: {e}")
            yield {"type": "error", "message": str(e)}
    
    async def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get current status of a task"""
        
        if task_id not in self.active_tasks:
            return None
        
        task_context = self.active_tasks[task_id]
        agents = self.task_agents.get(task_id, {})
        
        return {
            "task_id": task_id,
            "status": task_context.status.value,
            "description": task_context.description,
            "current_round": task_context.current_round,
            "max_rounds": task_context.max_rounds,
            "consensus_score": task_context.consensus_score,
            "created_at": task_context.created_at.isoformat(),
            "started_at": task_context.started_at.isoformat() if task_context.started_at else None,
            "completed_at": task_context.completed_at.isoformat() if task_context.completed_at else None,
            "agents": [
                {
                    "agent_id": agent.agent_id,
                    "role": agent.role.value,
                    "status": agent.status.value,
                    "metrics": agent.get_performance_metrics()
                }
                for agent in agents.values()
            ]
        }
    
    async def stop_task(self, task_id: str) -> bool:
        """Stop a running task"""
        
        if task_id not in self.active_tasks:
            return False
        
        task_context = self.active_tasks[task_id]
        
        if task_context.status == TaskStatus.RUNNING:
            task_context.status = TaskStatus.STOPPED
            task_context.completed_at = datetime.utcnow()
            
            # Stop all agents for this task
            if task_id in self.task_agents:
                for agent in self.task_agents[task_id].values():
                    try:
                        await agent.reset_session()
                    except Exception as e:
                        logger.error(f"Error stopping agent {agent.agent_id}: {e}")
            
            logger.info(f"Stopped task: {task_id}")
            return True
        
        return False
    
    async def list_active_tasks(self) -> List[Dict[str, Any]]:
        """List all active tasks"""
        
        active_tasks = []
        
        for task_id, task_context in self.active_tasks.items():
            if task_context.status in [TaskStatus.PENDING, TaskStatus.RUNNING]:
                active_tasks.append({
                    "task_id": task_id,
                    "status": task_context.status.value,
                    "description": task_context.description[:100] + "..." if len(task_context.description) > 100 else task_context.description,
                    "current_round": task_context.current_round,
                    "max_rounds": task_context.max_rounds,
                    "agent_count": len(task_context.participating_agents),
                    "created_at": task_context.created_at.isoformat()
                })
        
        return active_tasks
    
    async def get_health_status(self) -> Dict[str, Any]:
        """Get orchestrator health status"""
        
        total_tasks = len(self.active_tasks)
        active_task_count = len([
            t for t in self.active_tasks.values() 
            if t.status in [TaskStatus.PENDING, TaskStatus.RUNNING]
        ])
        
        total_agents = sum(len(agents) for agents in self.task_agents.values())
        
        return {
            "status": "healthy",
            "total_tasks": total_tasks,
            "active_tasks": active_task_count,
            "total_agents": total_agents,
            "agent_factory_status": agent_factory.get_factory_status(),
            "uptime_seconds": (datetime.utcnow() - datetime.utcnow()).total_seconds(),  # Placeholder
            "memory_usage": "N/A"  # Could add actual memory monitoring
        }
    
    async def _setup_monitoring(self):
        """Setup monitoring and health checks"""
        # Could implement periodic health checks, cleanup, etc.
        pass
    
    async def cleanup(self):
        """Cleanup orchestrator resources"""
        
        logger.info("Cleaning up Agent Orchestrator")
        
        self.shutdown_flag = True
        
        # Stop all active tasks
        for task_id in list(self.active_tasks.keys()):
            await self.stop_task(task_id)
        
        # Shutdown all agents
        await agent_factory.shutdown_all_agents()
        
        # Clear data structures
        self.active_tasks.clear()
        self.task_agents.clear()
        self.conversation_streams.clear()
        
        logger.info("Agent Orchestrator cleanup completed")


# Additional helper methods for the routes
    async def create_agent(
        self,
        agent_id: str,
        session_id: str,
        role: AgentRole,
        task_description: str,
        context_files: List[str] = None
    ) -> Dict[str, Any]:
        """Create individual agent (for direct API usage)"""
        
        agent = agent_factory.create_agent(
            role=role,
            task_description=task_description,
            agent_id=agent_id,
            session_id=session_id,
            context_files=context_files or []
        )
        
        return {
            "agent_id": agent_id,
            "session_id": session_id,
            "role": role.value,
            "status": agent.status.value,
            "created_at": datetime.utcnow().isoformat()
        }
    
    async def start_conversation(
        self,
        session_id: str,
        message_id: str,
        message: str,
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Start conversation with individual agent"""
        
        agent = agent_factory.get_agent_by_session(session_id)
        if not agent:
            raise ValueError(f"Agent session {session_id} not found")
        
        # Create minimal task context for individual conversation
        task_context = TaskContext(
            task_id=f"conv_{message_id}",
            description=message
        )
        
        response = await agent.process_task(task_context, [], [])
        
        return {
            "session_id": session_id,
            "message_id": message_id,
            "status": "completed",
            "response": response.content,
            "token_count": response.token_count
        }
    
    async def get_agent_status(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get agent status by session ID"""
        
        agent = agent_factory.get_agent_by_session(session_id)
        if not agent:
            return None
        
        return {
            "agent_id": agent.agent_id,
            "session_id": session_id,
            "role": agent.role.value,
            "status": agent.status.value,
            "metrics": agent.get_performance_metrics()
        }
    
    async def delete_agent(self, session_id: str) -> bool:
        """Delete agent by session ID"""
        
        agent = agent_factory.get_agent_by_session(session_id)
        if not agent:
            return False
        
        return await agent_factory.shutdown_agent(agent.agent_id)
    
    async def get_consensus_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get consensus status for a task"""
        
        if task_id not in self.active_tasks:
            return None
        
        task_context = self.active_tasks[task_id]
        
        if not task_context.conversation_rounds:
            return {
                "task_id": task_id,
                "consensus_level": "none",
                "consensus_score": 0.0,
                "rounds_completed": 0,
                "details": "No conversation rounds completed yet"
            }
        
        latest_round = task_context.conversation_rounds[-1]
        
        return {
            "task_id": task_id,
            "consensus_level": latest_round.consensus_level.value,
            "consensus_score": latest_round.consensus_score,
            "rounds_completed": len(task_context.conversation_rounds),
            "current_round": task_context.current_round,
            "max_rounds": task_context.max_rounds,
            "is_consensus_reached": task_context.is_consensus_reached,
            "should_meta_block": task_context.should_meta_block,
            "details": f"Round {latest_round.round_number}: {latest_round.consensus_level.value}"
        }
    
    async def list_active_agents(self) -> List[Dict[str, Any]]:
        """List all active agents across all tasks"""
        
        active_agents = []
        
        for task_id, agents in self.task_agents.items():
            task_context = self.active_tasks.get(task_id)
            
            for agent in agents.values():
                active_agents.append({
                    "agent_id": agent.agent_id,
                    "session_id": agent.session_id,
                    "role": agent.role.value,
                    "status": agent.status.value,
                    "task_id": task_id,
                    "task_status": task_context.status.value if task_context else "unknown",
                    "metrics": agent.get_performance_metrics()
                })
        
        return active_agents