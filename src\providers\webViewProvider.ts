import * as vscode from 'vscode';
import * as path from 'path';
import { serviceRegistry } from '../utils/serviceRegistry';

export class WebViewProvider implements vscode.Disposable {
  private panel: vscode.WebviewPanel | undefined;
  private disposables: vscode.Disposable[] = [];

  constructor(private context: vscode.ExtensionContext) {}

  public async createOrShow(): Promise<void> {
    const column = vscode.window.activeTextEditor
      ? vscode.window.activeTextEditor.viewColumn
      : undefined;

    // If panel exists, just reveal it
    if (this.panel) {
      this.panel.reveal(column);
      return;
    }

    // Create new panel
    this.panel = vscode.window.createWebviewPanel(
      'metamorphic-reactor',
      'Metamorphic Reactor',
      column || vscode.ViewColumn.One,
      {
        enableScripts: true,
        retainContextWhenHidden: true,
        localResourceRoots: [
          vscode.Uri.file(path.join(this.context.extensionPath, 'webview')),
          vscode.Uri.file(path.join(this.context.extensionPath, 'out'))
        ]
      }
    );

    // Set panel icon
    this.panel.iconPath = vscode.Uri.file(path.join(this.context.extensionPath, 'resources', 'icon.png'));

    // Set webview content
    this.panel.webview.html = this.getWebviewContent();

    // Setup event handlers
    this.setupEventHandlers();
    
    console.log('WebView panel created and initialized');
  }

  private setupEventHandlers(): void {
    if (!this.panel) return;

    // Handle messages from the webview
    this.panel.webview.onDidReceiveMessage(
      (message) => this.handleWebviewMessage(message),
      undefined,
      this.disposables
    );

    // Handle panel disposal
    this.panel.onDidDispose(
      () => {
        console.log('WebView panel disposed');
        this.panel = undefined;
        this.cleanup();
      },
      undefined,
      this.disposables
    );

    // Handle panel state changes
    this.panel.onDidChangeViewState(
      (e) => {
        console.log('WebView panel state changed:', e.webviewPanel.active ? 'active' : 'inactive');
        if (e.webviewPanel.active) {
          this.refreshAgentStatus();
        }
      },
      undefined,
      this.disposables
    );
  }

  private cleanup(): void {
    // Clean up any running processes or connections
    console.log('Cleaning up WebView provider resources');
  }

  public get isActive(): boolean {
    return this.panel?.active ?? false;
  }

  public get isVisible(): boolean {
    return this.panel?.visible ?? false;
  }

  private getWebviewContent(): string {

    return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Metamorphic Reactor</title>
  <style>
    body {
      margin: 0;
      padding: 20px;
      font-family: var(--vscode-font-family);
      background-color: var(--vscode-editor-background);
      color: var(--vscode-editor-foreground);
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
    }
    .header {
      text-align: center;
      margin-bottom: 30px;
    }
    .task-input {
      margin-bottom: 20px;
    }
    .task-input textarea {
      width: 100%;
      height: 100px;
      background-color: var(--vscode-input-background);
      border: 1px solid var(--vscode-input-border);
      color: var(--vscode-input-foreground);
      padding: 10px;
      border-radius: 4px;
      resize: vertical;
    }
    .button {
      background-color: var(--vscode-button-background);
      color: var(--vscode-button-foreground);
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      margin-right: 10px;
    }
    .button:hover {
      background-color: var(--vscode-button-hoverBackground);
    }
    .agent-status {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;
      margin-top: 30px;
    }
    .agent-card {
      border: 1px solid var(--vscode-panel-border);
      border-radius: 8px;
      padding: 15px;
      background-color: var(--vscode-panel-background);
    }
    .agent-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
    }
    .status-indicator {
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: bold;
    }
    .status-idle { background-color: #6c757d; color: white; }
    .status-running { background-color: #28a745; color: white; }
    .status-error { background-color: #dc3545; color: white; }
    .token-stream {
      background-color: var(--vscode-textCodeBlock-background);
      border: 1px solid var(--vscode-textCodeBlock-border);
      border-radius: 4px;
      padding: 10px;
      font-family: var(--vscode-editor-font-family);
      font-size: 12px;
      height: 200px;
      overflow-y: auto;
      white-space: pre-wrap;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🧬 Metamorphic Reactor</h1>
      <p>Multi-Agent LLM Orchestration with Autonomous Consensus</p>
    </div>

    <div class="task-input">
      <label for="taskDescription">Task Description:</label>
      <textarea id="taskDescription" placeholder="Describe the task you want the agents to complete..."></textarea>
      <div style="margin-top: 10px;">
        <button class="button" onclick="startTask()">Start Task</button>
        <button class="button" onclick="stopTask()">Stop Task</button>
        <button class="button" onclick="clearOutput()">Clear Output</button>
      </div>
    </div>

    <div class="agent-status" id="agentStatus">
      <!-- Agent cards will be populated dynamically -->
    </div>
  </div>

  <script>
    const vscode = acquireVsCodeApi();

    function startTask() {
      const taskDescription = document.getElementById('taskDescription').value;
      if (!taskDescription.trim()) {
        alert('Please enter a task description');
        return;
      }
      
      vscode.postMessage({
        command: 'startTask',
        taskDescription: taskDescription
      });
    }

    function stopTask() {
      vscode.postMessage({
        command: 'stopTask'
      });
    }

    function clearOutput() {
      const agentCards = document.querySelectorAll('.token-stream');
      agentCards.forEach(card => card.textContent = '');
    }

    // Handle messages from the extension
    window.addEventListener('message', event => {
      const message = event.data;
      
      switch (message.command) {
        case 'updateAgentStatus':
          updateAgentStatus(message.agents);
          break;
        case 'tokenStream':
          appendTokenStream(message.agentId, message.token);
          break;
        case 'taskComplete':
          handleTaskComplete(message.result);
          break;
      }
    });

    function updateAgentStatus(agents) {
      const container = document.getElementById('agentStatus');
      container.innerHTML = '';
      
      agents.forEach(agent => {
        const card = document.createElement('div');
        card.className = 'agent-card';
        card.innerHTML = \`
          <div class="agent-header">
            <h3>\${agent.id}</h3>
            <span class="status-indicator status-\${agent.status}">\${agent.status.toUpperCase()}</span>
          </div>
          <p><strong>Role:</strong> \${agent.role}</p>
          <div class="token-stream" id="stream-\${agent.id}"></div>
        \`;
        container.appendChild(card);
      });
    }

    function appendTokenStream(agentId, token) {
      const streamElement = document.getElementById(\`stream-\${agentId}\`);
      if (streamElement) {
        streamElement.textContent += token;
        streamElement.scrollTop = streamElement.scrollHeight;
      }
    }

    function handleTaskComplete(result) {
      alert(\`Task completed: \${result.status}\`);
    }
  </script>
</body>
</html>`;
  }

  private async handleWebviewMessage(message: any): Promise<void> {
    console.log('Received webview message:', message.command);
    
    try {
      switch (message.command) {
        case 'startTask':
          await this.handleStartTask(message.taskDescription);
          break;
        case 'stopTask':
          await this.handleStopTask();
          break;
        case 'getAgentStatus':
          await this.refreshAgentStatus();
          break;
        case 'clearOutput':
          this.sendMessage({ command: 'clearOutput' });
          break;
        default:
          console.warn('Unknown webview message:', message);
      }
    } catch (error) {
      console.error('Error handling webview message:', error);
      this.sendMessage({
        command: 'error',
        message: `Error: ${error}`
      });
    }
  }

  private async handleStartTask(taskDescription: string): Promise<void> {
    try {
      console.log('Starting task:', taskDescription);
      
      // Get WorkerThreadManager from service registry
      const workerThreadManager = serviceRegistry.getWorkerThreadManager();
      
      if (workerThreadManager) {
        // Generate task ID
        const taskId = `task-${Date.now()}`;
        
        // Spawn agents
        const agentIds = await workerThreadManager.spawnAgents(taskId);
        console.log('Spawned agents:', agentIds);
        
        // Get agent status and send to webview
        const agentStatus = workerThreadManager.getWorkerStatus();
        this.sendMessage({
          command: 'updateAgentStatus',
          agents: agentStatus
        });
        
        // Send task started notification
        this.sendMessage({
          command: 'taskStarted',
          taskId,
          taskDescription
        });
        
        // TODO: Start AutoGen orchestration service
        
      } else {
        throw new Error('WorkerThreadManager not available');
      }
      
    } catch (error) {
      console.error('Error starting task:', error);
      this.sendMessage({
        command: 'error',
        message: `Failed to start task: ${error}`
      });
    }
  }

  private async handleStopTask(): Promise<void> {
    try {
      console.log('Stopping task');
      
      // Get WorkerThreadManager from service registry
      const workerThreadManager = serviceRegistry.getWorkerThreadManager();
      
      if (workerThreadManager) {
        // Terminate all workers
        await workerThreadManager.terminateWorkers();
        
        // Send task stopped notification
        this.sendMessage({
          command: 'taskStopped'
        });
        
        // Clear agent status
        this.sendMessage({
          command: 'updateAgentStatus',
          agents: []
        });
        
      } else {
        console.warn('WorkerThreadManager not available');
      }
      
    } catch (error) {
      console.error('Error stopping task:', error);
      this.sendMessage({
        command: 'error',
        message: `Failed to stop task: ${error}`
      });
    }
  }

  private async refreshAgentStatus(): Promise<void> {
    try {
      const workerThreadManager = serviceRegistry.getWorkerThreadManager();
      
      if (workerThreadManager) {
        const agentStatus = workerThreadManager.getWorkerStatus();
        this.sendMessage({
          command: 'updateAgentStatus',
          agents: agentStatus
        });
      }
    } catch (error) {
      console.error('Error refreshing agent status:', error);
    }
  }

  public sendMessage(message: any): void {
    if (this.panel) {
      this.panel.webview.postMessage(message);
    }
  }

  public dispose(): void {
    if (this.panel) {
      this.panel.dispose();
    }
    
    this.disposables.forEach(disposable => disposable.dispose());
    this.disposables = [];
  }
}