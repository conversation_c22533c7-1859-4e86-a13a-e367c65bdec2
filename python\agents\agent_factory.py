"""
Agent Factory for Metamorphic Reactor
Creates and manages agent instances based on role and configuration
"""

import logging
from typing import Dict, Any, Optional
import uuid

from .agent_types import Agent<PERSON>onfig, AgentRole
from .base_agent import BaseAgent
from .planner_agent import PlannerAgent
from .critic_agent import CriticAgent
from .additional_agent import AdditionalAgent

logger = logging.getLogger(__name__)

class AgentFactory:
    """Factory class for creating and managing agent instances"""
    
    # Agent class registry
    AGENT_CLASSES = {
        AgentRole.PLANNER: PlannerAgent,
        AgentRole.CRITIC: CriticAgent,
        AgentRole.ADDITIONAL: AdditionalAgent
    }
    
    def __init__(self):
        self.active_agents: Dict[str, BaseAgent] = {}
        self.agent_sessions: Dict[str, str] = {}  # session_id -> agent_id mapping
        
    def create_agent(
        self,
        role: AgentRole,
        task_description: str,
        agent_id: Optional[str] = None,
        session_id: Optional[str] = None,
        **kwargs
    ) -> BaseAgent:
        """
        Create a new agent instance
        
        Args:
            role: Agent role (planner, critic, additional)
            task_description: Task description for the agent
            agent_id: Unique identifier for the agent (generated if not provided)
            session_id: Session identifier (generated if not provided)
            **kwargs: Additional configuration parameters
            
        Returns:
            BaseAgent: Configured agent instance
            
        Raises:
            ValueError: If role is not supported
        """
        
        if role not in self.AGENT_CLASSES:
            raise ValueError(f"Unsupported agent role: {role}")
        
        # Generate IDs if not provided
        if not agent_id:
            agent_id = f"{role.value}_{uuid.uuid4().hex[:8]}"
        
        if not session_id:
            session_id = f"session_{uuid.uuid4().hex[:12]}"
        
        # Create agent configuration
        config = AgentConfig(
            role=role,
            agent_id=agent_id,
            session_id=session_id,
            task_description=task_description,
            **kwargs
        )
        
        # Instantiate agent
        agent_class = self.AGENT_CLASSES[role]
        agent = agent_class(config)
        
        # Register agent
        self.active_agents[agent_id] = agent
        self.agent_sessions[session_id] = agent_id
        
        logger.info(f"Created {role.value} agent: {agent_id} (session: {session_id})")
        
        return agent
    
    def get_agent(self, agent_id: str) -> Optional[BaseAgent]:
        """Get agent by ID"""
        return self.active_agents.get(agent_id)
    
    def get_agent_by_session(self, session_id: str) -> Optional[BaseAgent]:
        """Get agent by session ID"""
        agent_id = self.agent_sessions.get(session_id)
        if agent_id:
            return self.active_agents.get(agent_id)
        return None
    
    def get_agents_by_role(self, role: AgentRole) -> Dict[str, BaseAgent]:
        """Get all agents with specified role"""
        return {
            agent_id: agent 
            for agent_id, agent in self.active_agents.items()
            if agent.role == role
        }
    
    def list_active_agents(self) -> Dict[str, Dict[str, Any]]:
        """List all active agents with their status"""
        return {
            agent_id: {
                "role": agent.role.value,
                "session_id": agent.session_id,
                "status": agent.status.value,
                "metrics": agent.get_performance_metrics()
            }
            for agent_id, agent in self.active_agents.items()
        }
    
    def create_agent_team(
        self,
        task_description: str,
        team_size: int = 3,
        task_id: Optional[str] = None,
        **kwargs
    ) -> Dict[AgentRole, BaseAgent]:
        """
        Create a complete agent team for multi-agent collaboration
        
        Args:
            task_description: Task description for all agents
            team_size: Number of agents (2-5, defaults to 3)
            task_id: Task identifier for session management
            **kwargs: Additional configuration parameters
            
        Returns:
            Dict[AgentRole, BaseAgent]: Dictionary of role -> agent mappings
            
        Raises:
            ValueError: If team_size is invalid
        """
        
        if team_size < 2 or team_size > 5:
            raise ValueError("Team size must be between 2 and 5 agents")
        
        if not task_id:
            task_id = f"task_{uuid.uuid4().hex[:8]}"
        
        team = {}
        
        # Always create planner and critic
        team[AgentRole.PLANNER] = self.create_agent(
            role=AgentRole.PLANNER,
            task_description=task_description,
            agent_id=f"{task_id}_planner",
            session_id=f"{task_id}_planner_session",
            **kwargs
        )
        
        team[AgentRole.CRITIC] = self.create_agent(
            role=AgentRole.CRITIC,
            task_description=task_description,
            agent_id=f"{task_id}_critic",
            session_id=f"{task_id}_critic_session",
            **kwargs
        )
        
        # Create additional agents based on team size
        additional_count = team_size - 2
        for i in range(additional_count):
            agent_id = f"{task_id}_additional_{i+1}"
            session_id = f"{task_id}_additional_{i+1}_session"
            
            team[f"additional_{i+1}"] = self.create_agent(
                role=AgentRole.ADDITIONAL,
                task_description=task_description,
                agent_id=agent_id,
                session_id=session_id,
                **kwargs
            )
        
        logger.info(f"Created agent team for task {task_id}: {team_size} agents")
        
        return team
    
    async def shutdown_agent(self, agent_id: str) -> bool:
        """
        Shutdown and remove an agent
        
        Args:
            agent_id: Agent identifier
            
        Returns:
            bool: True if agent was found and shutdown, False otherwise
        """
        
        agent = self.active_agents.get(agent_id)
        if not agent:
            return False
        
        try:
            await agent.shutdown()
            
            # Remove from registries
            del self.active_agents[agent_id]
            
            # Remove session mapping
            session_to_remove = None
            for session_id, mapped_agent_id in self.agent_sessions.items():
                if mapped_agent_id == agent_id:
                    session_to_remove = session_id
                    break
            
            if session_to_remove:
                del self.agent_sessions[session_to_remove]
            
            logger.info(f"Shutdown agent: {agent_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error shutting down agent {agent_id}: {e}")
            return False
    
    async def shutdown_all_agents(self) -> int:
        """
        Shutdown all active agents
        
        Returns:
            int: Number of agents shutdown
        """
        
        shutdown_count = 0
        agent_ids = list(self.active_agents.keys())
        
        for agent_id in agent_ids:
            if await self.shutdown_agent(agent_id):
                shutdown_count += 1
        
        logger.info(f"Shutdown {shutdown_count} agents")
        return shutdown_count
    
    def get_factory_status(self) -> Dict[str, Any]:
        """Get factory status and statistics"""
        
        role_counts = {}
        for agent in self.active_agents.values():
            role = agent.role.value
            role_counts[role] = role_counts.get(role, 0) + 1
        
        return {
            "total_agents": len(self.active_agents),
            "active_sessions": len(self.agent_sessions),
            "agents_by_role": role_counts,
            "supported_roles": [role.value for role in self.AGENT_CLASSES.keys()]
        }
    
    def validate_team_configuration(self, team_size: int, subscription_tier: str = "free") -> bool:
        """
        Validate team configuration against subscription limits
        
        Args:
            team_size: Requested team size
            subscription_tier: Subscription tier (free, pro, dev)
            
        Returns:
            bool: True if configuration is valid
        """
        
        max_agents = {
            "free": 2,
            "pro": 3,
            "dev": 5,
            "dev+": 5
        }
        
        limit = max_agents.get(subscription_tier.lower(), 2)
        return team_size <= limit
    
    def __repr__(self) -> str:
        return f"<AgentFactory(active_agents={len(self.active_agents)}, sessions={len(self.agent_sessions)})>"


# Global factory instance
agent_factory = AgentFactory()