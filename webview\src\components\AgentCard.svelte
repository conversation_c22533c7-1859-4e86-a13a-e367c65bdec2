<script lang="ts">
  import { onMount } from 'svelte';
  
  export let agent: {
    id: string;
    status: 'idle' | 'running' | 'error' | 'stopped';
    role: 'planner' | 'critic' | 'additional';
  };

  let outputElement: HTMLElement;
  let isStreaming = false;

  $: statusClass = {
    idle: 'bg-gray-500',
    running: 'bg-green-500 animate-pulse',
    error: 'bg-red-500',
    stopped: 'bg-gray-600'
  }[agent.status];

  $: roleEmoji = {
    planner: '🧠',
    critic: '🔍',
    additional: '⚡'
  }[agent.role];

  $: isStreaming = agent.status === 'running';

  onMount(() => {
    // Listen for token streams for this specific agent
    window.addEventListener('message', handleTokenStream);
    
    return () => {
      window.removeEventListener('message', handleTokenStream);
    };
  });

  function handleTokenStream(event: MessageEvent) {
    const message = event.data;
    
    if (message.command === 'tokenStream' && message.agentId === agent.id) {
      if (outputElement) {
        outputElement.textContent += message.token;
        outputElement.scrollTop = outputElement.scrollHeight;
      }
    }
    
    if (message.command === 'clearOutput') {
      if (outputElement) {
        outputElement.textContent = '';
      }
    }
  }
</script>

<div class="bg-gray-800 border border-gray-700 rounded-lg p-4">
  <div class="flex justify-between items-center mb-3">
    <h3 class="font-semibold text-lg flex items-center gap-2">
      <span>{roleEmoji}</span>
      <span>{agent.id}</span>
    </h3>
    <span class="{statusClass} text-white text-xs font-bold px-2 py-1 rounded-full">
      {agent.status.toUpperCase()}
    </span>
  </div>
  
  <p class="text-gray-400 text-sm mb-3">
    <strong>Role:</strong> {agent.role}
  </p>
  
  <div class="bg-black border border-gray-600 rounded p-3 h-32 overflow-y-auto relative">
    <div 
      bind:this={outputElement}
      class="font-mono text-xs text-green-400" 
      id="stream-{agent.id}"
    >
      {#if isStreaming}
        <div class="flex items-center gap-2 text-gray-400">
          <div class="w-2 h-2 bg-green-400 rounded-full animate-ping"></div>
          Thinking...
        </div>
      {:else}
        <div class="text-gray-500">Waiting for output...</div>
      {/if}
    </div>
    
    {#if isStreaming}
      <div class="absolute top-2 right-2">
        <div class="w-3 h-3 border-2 border-green-400 border-t-transparent rounded-full animate-spin"></div>
      </div>
    {/if}
  </div>
</div>