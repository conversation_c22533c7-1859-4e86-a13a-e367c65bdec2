<script lang="ts">
  import AgentCard from './AgentCard.svelte';
  
  export let agents: any[] = [];
</script>

<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
  {#each agents as agent (agent.id)}
    <AgentCard {agent} />
  {/each}
  
  {#if agents.length === 0}
    <div class="col-span-full text-center py-12">
      <div class="text-gray-400 text-lg">
        <div class="mb-4">🤖</div>
        <p>No agents running</p>
        <p class="text-sm mt-2">Start a task to see agent activity</p>
      </div>
    </div>
  {/if}
</div>